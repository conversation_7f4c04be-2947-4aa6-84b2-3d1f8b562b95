name: deepar_flutter_plus
description: Enhanced DeepAR Flutter SDK with support for loading effects from assets, file paths, and URLs with caching
version: 0.2.0
homepage: https://deepar.ai/
repository: https://github.com/Ifoegbu1/deepar-flutter-plus
issue_tracker: https://github.com/Ifoegbu1/deepar-flutter-plus/issues

environment:
  sdk: ">=2.16.1 <4.0.0"
  flutter: ">=2.5.0"

dependencies:
  flutter:
    sdk: flutter
  permission_handler: ^11.4.0
  vector_math: ^2.1.4
  flutter_cache_manager: ^3.4.1

dev_dependencies:
  flutter_lints: ^1.0.0
  flutter_test:
    sdk: flutter

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec
# The following section is specific to Flutter.
flutter:
  # This section identifies this Flutter project as a plugin project.
  # The 'pluginClass' and Android 'package' identifiers should not ordinarily
  # be modified. They are used by the tooling to maintain consistency when
  # adding or updating assets for this project.
  plugin:
    platforms:
      android:
        package: com.deepar.ai
        pluginClass: DeepArPlugin
      ios:
        pluginClass: DeepArPlugin
  # To add assets to your plugin package, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg
  #
  # For details regarding assets in packages, see
  # https://flutter.dev/assets-and-images/#from-packages
  #
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.
  # To add custom fonts to your plugin package, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts in packages, see
  # https://flutter.dev/custom-fonts/#from-packages

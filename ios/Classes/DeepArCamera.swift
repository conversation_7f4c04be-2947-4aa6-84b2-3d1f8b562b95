//
//  DeepArCamera.swift
//  deepar_flutter
//
//  Created by <PERSON><PERSON><PERSON> on 14/07/22.
//
import DeepAR
import Foundation
import AVKit

extension String {
    static func isNilOrEmpty(string: String?) -> <PERSON>ol {
        guard let value = string else { return true }

        return value.trimmingCharacters(in: .whitespaces).isEmpty
    }
}

enum PictureQuality: String {
    case low   = "low"
    case medium   = "medium"
    case high = "high"
    case veryHigh = "veryHigh"
}

enum DeepArResponse: String {
    case videoStarted   = "videoStarted"
    case videoCompleted   = "videoCompleted"
    case videoError = "videoError"
    case screenshotTaken = "screenshotTaken"
}

class DeepARCameraFactory: NSObject, FlutterPlatformViewFactory {
    private var messenger: FlutterBinaryMessenger
    private var registrar: FlutterPluginRegistrar

    init(messenger: FlutterBinaryMessenger, registrar: FlutterPluginRegistrar) {
        self.messenger = messenger
        self.registrar = registrar;
        super.init()
    }

    func create(
        withFrame frame: CGRect,
        viewIdentifier viewId: Int64,
        arguments args: Any?
    ) -> FlutterPlatformView {
        return DeepARCameraView(
            frame: frame,
            viewIdentifier: viewId,
            arguments: args,
            binaryMessenger: messenger,registrar: registrar)
    }
    public func createArgsCodec() -> FlutterMessageCodec & NSObjectProtocol {
        return FlutterStandardMessageCodec.sharedInstance()
    }
}


class DeepARCameraView: NSObject, FlutterPlatformView, DeepARDelegate {
    // Face detection state
    private var isFaceVisible: Bool = false
    private var isRecordingInProcess: Bool = false

    private var deepAR: DeepAR!
    private var cameraController: CameraController!
    private var arView: UIView!
    private var frame:CGRect!


    private var pictureQuality:PictureQuality!
    private var licenseKey:String!
    private var videoFilePath:String!
    private var screenshotFilePath:String!

    private var channel:FlutterMethodChannel!
    private var registrar: FlutterPluginRegistrar!

    init(
        frame: CGRect,
        viewIdentifier viewId: Int64,
        arguments args: Any?,
        binaryMessenger messenger: FlutterBinaryMessenger?,
        registrar: FlutterPluginRegistrar
    ) {
        super.init()
        self.frame = frame;
        self.registrar = registrar
        if let dict = args as? [String: Any] {
            self.licenseKey = (dict["license_key"] as? String ?? "")
            self.pictureQuality = PictureQuality.init(rawValue: dict["resolution"] as? String ?? "medium")
        }
        channel = FlutterMethodChannel(name: "deep_ar/view/" + String(viewId), binaryMessenger: messenger!);
        channel.setMethodCallHandler(methodHandler);
        createNativeView()
    }



    func methodHandler(_ call: FlutterMethodCall, result: @escaping FlutterResult){
        let args = call.arguments as? [String : Any]
        switch call.method{
        case "switch_effect":
            guard let effect = args?["effect"] as? String else {
                result(FlutterError(code: "MISSING_PARAM", message: "Missing effect parameter", details: nil))
                return
            }

            print("iOS switchEffect called with path: \(effect)")

            // Check if it's a file path
            if FileManager.default.fileExists(atPath: effect) {
                print("iOS: Loading effect from file path: \(effect)")
                deepAR.switchEffect(withSlot: "effect", path: effect)
                result("Effect loaded from file path")
                return
            }

            // Try as asset
            if let key = registrar?.lookupKey(forAsset: effect) {
                if let path = Bundle.main.path(forResource: key, ofType: nil) {
                    print("iOS: Loading effect from asset: \(path)")
                    deepAR.switchEffect(withSlot: "effect", path: path)
                    result("Effect loaded from asset")
                    return
                } else {
                    print("iOS: Asset not found: \(key)")
                }
            }

            // If we get here, the effect couldn't be loaded
            print("iOS: Failed to load effect: \(effect)")
            result(FlutterError(code: "EFFECT_NOT_FOUND", message: "Effect not found: \(effect)", details: nil))

        case "switch_face_mask":
            let mask:String = args?["effect"] as! String
            // Check if it's a file path
            if FileManager.default.fileExists(atPath: mask) {
                deepAR.switchEffect(withSlot: "mask", path: mask)
            } else {
                // Try as asset
                let key = registrar?.lookupKey(forAsset: mask)
                let path = Bundle.main.path(forResource: key, ofType: nil)
                deepAR.switchEffect(withSlot: "mask", path: path)
            }
            result("switchFaceMask called successfully")

        case "switch_filter":
            let filter:String = args?["effect"] as! String
            // Check if it's a file path
            if FileManager.default.fileExists(atPath: filter) {
                deepAR.switchEffect(withSlot: "filters", path: filter)
            } else {
                // Try as asset
                let key = registrar?.lookupKey(forAsset: filter)
                let path = Bundle.main.path(forResource: key, ofType: nil)
                deepAR.switchEffect(withSlot: "filters", path: path)
            }
            result("switchFilter called successfully")

        case "switchEffectWithSlot":
            let slot:String = args?["slot"] as! String
            let path:String = args?["path"] as! String
            let face = args?["face"] as! Int
            let targetGameObject = args?["targetGameObject"] as? String
            let isGameTargetEmpty = String.isNilOrEmpty(string: targetGameObject)

            // Check if it's a file path
            let effectPath: String
            if FileManager.default.fileExists(atPath: path) {
                effectPath = path
            } else {
                // Try as asset
                let key = registrar?.lookupKey(forAsset: path)
                effectPath = Bundle.main.path(forResource: key, ofType: nil) ?? path
            }

            if !isGameTargetEmpty {
                deepAR.switchEffect(withSlot: slot, path: effectPath, face: face, targetGameObject: targetGameObject)
            } else {
                deepAR.switchEffect(withSlot: slot, path: effectPath, face: face)
            }
            result("switchEffectWithSlot called successfully")


        case "start_recording_video":
            startRecordingVideo();
            result("STARTING_TO_RECORD");
        case "stop_recording_video":
            finishRecordingVideo();
            result("STOPPING_RECORDING");
        case "get_resolution":
            result(String(1280) + " " + String(720));
        case "take_screenshot":
            deepAR.takeScreenshot()
            result("SCREENSHOT_TRIGGERED");
        case "flip_camera":
            cameraController.position = cameraController.position == .back ? .front : .back
            result(true);
        case "toggle_flash":
            let isFlash:Bool = toggleFlash()
            result(isFlash);
        case "fireTrigger":
            let trigger:String = args?["trigger"] as! String
            deepAR.fireTrigger(trigger)
            result("fireTrigger called successfully")
        case "showStats":
            let enabled:Bool = args?["enabled"] as! Bool
            deepAR.showStats(enabled)
            result("showStats called successfully")
        case "simulatePhysics":
            let enabled:Bool = args?["enabled"] as! Bool
            deepAR.simulatePhysics(enabled)
            result("simulatePhysics called successfully")
        case "showColliders":
            let enabled:Bool = args?["enabled"] as! Bool
            deepAR.showColliders(enabled)
            result("showColliders called successfully")
        case "moveGameObject":
            let selectedGameObjectName:String = args?["selectedGameObjectName"] as! String
            let targetGameObjectName:String = args?["targetGameObjectName"] as! String
            deepAR.moveGameObject(selectedGameObjectName, targetGameObjectname: targetGameObjectName)
            result("moveGameObject called successfully")
        case "changeParameter":
            let gameObject:String = args?["gameObject"] as! String
            let component:String = args?["component"] as! String
            let parameter:String = args?["parameter"] as! String
            let newParameter = args?["newParameter"]

            if newParameter == nil  {
                let x = Float(args?["x"] as! Double)
                let y = Float(args?["y"] as! Double)
                let z = Float(args?["z"] as! Double)
                let w = args?["w"]

                if w == nil {
                    let vector3:Vector3 = Vector3(x: x , y: y, z: z)
                    deepAR.changeParameter(gameObject, component: component, parameter: parameter, vector3Value: vector3 )
                }else{
                    let vector4:Vector4 = Vector4(x: x , y: y, z: z, w: Float(w as! Double) )
                    deepAR.changeParameter(gameObject, component: component, parameter: parameter, vectorValue: vector4 )
                }

            }
            else if newParameter is Bool {
                deepAR.changeParameter(gameObject, component: component, parameter: parameter, boolValue: newParameter as! Bool )
            }
            else if newParameter is Double {
                deepAR.changeParameter(gameObject, component: component, parameter: parameter, floatValue: Float(newParameter as! Double))
            }
            else if newParameter is String {
                deepAR.changeParameter(gameObject, component: component, parameter: parameter, stringValue: newParameter as? String)
            }

            result("changeParameter called successfully")

        case "process_static_image":
            guard let imagePath = args?["image_path"] as? String,
                  let outputPath = args?["output_path"] as? String else {
                result(FlutterError(code: "MISSING_PARAMS", message: "Missing image_path or output_path", details: nil))
                return
            }

            processStaticImage(imagePath: imagePath, outputPath: outputPath) { success, error in
                if success {
                    result(outputPath)
                } else {
                    result(FlutterError(code: "PROCESSING_ERROR", message: error ?? "Unknown error", details: nil))
                }
            }

        case "destroy":
            print("iOS: Destroying DeepAR camera view")
            // Stop camera first
            cameraController.stopCamera()

            // Add a small delay before shutting down DeepAR to ensure camera resources are released
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                self.deepAR.shutdown()
                print("iOS: DeepAR shutdown completed")
                result("SHUTDOWN")
            }
            return // Early return to avoid calling result twice
        default:
            result("No platform method found")
        }

    }

    func view() -> UIView {
        return arView
    }

    func createNativeView(){
        self.deepAR = DeepAR()
        self.deepAR.delegate = self
        self.deepAR.setLicenseKey(licenseKey)

        cameraController = CameraController()

        cameraController.preset = presetForPictureQuality(pictureQuality: pictureQuality);
        cameraController.videoOrientation = .portrait;
        cameraController.deepAR = self.deepAR
        self.deepAR.videoRecordingWarmupEnabled = false;


        deepAR.changeLiveMode(true);

        self.arView = self.deepAR.createARView(withFrame: self.frame)
        cameraController.startCamera()

        NotificationCenter.default.addObserver(self, selector: #selector(orientationDidChange), name: UIDevice.orientationDidChangeNotification, object: nil)
    }

    func toggleFlash() -> Bool {
        if cameraController.position == .front {
            // Prevent flash when front camera is on
            return false
        }

        guard let captureDevice = AVCaptureDevice.default(for: AVMediaType.video) else {
            return false
        }

        if captureDevice.hasTorch {
            do {
                let _: () = try captureDevice.lockForConfiguration()
            } catch {
                print("Error while lockForConfiguration()")
            }

            if captureDevice.isTorchActive {
                captureDevice.torchMode = AVCaptureDevice.TorchMode.off
            } else {

                do {
                    let _ = try captureDevice.setTorchModeOn(level: 1.0)
                    return true // flash ON
                } catch {
                    print("Error while setTorchModeOn()")
                }
            }

            captureDevice.unlockForConfiguration()
        }

        return false // flash OFF
    }


    func startRecordingVideo(){
        let width: Int32 = Int32(deepAR.renderingResolution.width)
        let height: Int32 =  Int32(deepAR.renderingResolution.height)

        deepAR.startVideoRecording(withOutputWidth: width, outputHeight: height)
        isRecordingInProcess = true
    }

    func finishRecordingVideo(){
        deepAR.finishVideoRecording();
    }

    func didFinishPreparingForVideoRecording() {
        NSLog("didFinishPreparingForVideoRecording!!!!!")
    }

    func didStartVideoRecording() {
        NSLog("didStartVideoRecording!!!!!")
        videoResult(callerResponse: DeepArResponse.videoStarted, message: "video started")
    }

    func recordingFailedWithError(_ error: Error!) {
        NSLog("recordingFailedWithError!!!!!")
        videoResult(callerResponse: DeepArResponse.videoError, message: "video error")
    }

    func didFinishVideoRecording(_ videoFilePath: String!) {

        NSLog("didFinishVideoRecording!!!!!")
        self.videoFilePath = videoFilePath
        videoResult(callerResponse: DeepArResponse.videoCompleted, message: "video completed")
    }

    func didFinishShutdown (){
        NSLog("didFinishShutdown!!!!!")
    }

    func faceVisiblityDidChange(_ faceVisible: Bool) {
        NSLog("Face visibility changed: \(faceVisible)")

        // Only notify Flutter if the state has changed
        if isFaceVisible != faceVisible {
            isFaceVisible = faceVisible

            // Notify Flutter about face visibility change
            var map = [String: Any]()
            map["is_visible"] = faceVisible
            channel.invokeMethod("on_face_visibility_changed", arguments: map)
        }
    }

    func didTakeScreenshot(_ screenshot: UIImage!) {
        if let data = screenshot.pngData() {

            let filename = FileManager.default.urls(for: .documentDirectory, in:.userDomainMask)[0] .appendingPathComponent(String(NSDate().timeIntervalSince1970).replacingOccurrences(of: ".", with: "-") + ".png")
            try? data.write(to: filename)
            screenshotFilePath = filename.path;
            screenshotResult(callerResponse: DeepArResponse.screenshotTaken, message: "Screenshot_taken")
        }

    }

    func presetForPictureQuality(pictureQuality: PictureQuality) -> AVCaptureSession.Preset {
        switch pictureQuality {
        case .low:
            return AVCaptureSession.Preset.vga640x480;
        case .medium:
            return AVCaptureSession.Preset.vga640x480;
        case .high:
            return AVCaptureSession.Preset.hd1280x720
        case .veryHigh:
            return AVCaptureSession.Preset.hd1920x1080;
        }
    }

    func resolutionForPictureQuality (pictureQuality: PictureQuality) -> CGSize {
        switch pictureQuality {
        case .low:
            return CGSize(width: 640, height: 480);
        case .medium:
            return CGSize(width: 640, height: 480);
        case .high:
            return CGSize(width: 1280, height: 720);
        case .veryHigh:
            return CGSize(width: 1920, height: 1080);
        }
    }

    func videoResult(callerResponse: DeepArResponse, message: String) {
        var map = [String : String]()
        map["caller"] = callerResponse.rawValue
        map["message"] = message
        if callerResponse == DeepArResponse.videoCompleted {
            map["file_path"] = videoFilePath
        }

        channel.invokeMethod("on_video_result", arguments: map)
    }
    func screenshotResult(callerResponse: DeepArResponse, message: String) {
        var map = [String : String]()
        map["caller"] = callerResponse.rawValue
        map["message"] = message
        if callerResponse == DeepArResponse.screenshotTaken {
            map["file_path"] = screenshotFilePath
        }

        channel.invokeMethod("on_screenshot_result", arguments: map)
    }

    @objc
    private func orientationDidChange() {
        guard let orientation = UIApplication.shared.windows.first?.windowScene?.interfaceOrientation else { return }
        switch orientation {
        case .landscapeLeft:
            cameraController.videoOrientation = .landscapeLeft
            break
        case .landscapeRight:
            cameraController.videoOrientation = .landscapeRight
            break
        case .portrait:
            cameraController.videoOrientation = .portrait
            break
        case .portraitUpsideDown:
            cameraController.videoOrientation = .portraitUpsideDown
        default:
            break
        }
    }

    /// Process a static image with DeepAR effects
    /// - Parameters:
    ///   - imagePath: Path to the input image file
    ///   - outputPath: Path where the processed image will be saved
    ///   - completion: Callback with success status and error message if any
    private func processStaticImage(imagePath: String, outputPath: String, completion: @escaping (Bool, String?) -> Void) {
        // Check if the input file exists
        guard FileManager.default.fileExists(atPath: imagePath) else {
            completion(false, "Input image file not found at path: \(imagePath)")
            return
        }

        // Load the image
        guard let image = UIImage(contentsOfFile: imagePath) else {
            completion(false, "Failed to load image from path: \(imagePath)")
            return
        }

        // Convert UIImage to CVPixelBuffer
        guard let pixelBuffer = image.pixelBuffer() else {
            completion(false, "Failed to convert image to pixel buffer")
            return
        }

        // Save current camera state
        let wasLiveMode = deepAR.isLiveMode()

        // Switch to non-live mode for processing static images
        deepAR.changeLiveMode(false)

        // Process the image
        deepAR.processFrame(pixelBuffer, mirror: false)

        // Take screenshot of the processed image
        deepAR.takeScreenshot()

        // Set up a completion handler for the screenshot
        let originalDelegate = deepAR.delegate
        deepAR.delegate = nil

        // Create a temporary delegate to handle the screenshot
        let tempDelegate = TempScreenshotDelegate { screenshot in
            // Save the processed image
            if let data = screenshot.pngData() {
                do {
                    let url = URL(fileURLWithPath: outputPath)
                    try data.write(to: url)

                    // Restore original state
                    self.deepAR.changeLiveMode(wasLiveMode)
                    self.deepAR.delegate = originalDelegate

                    completion(true, nil)
                } catch {
                    self.deepAR.changeLiveMode(wasLiveMode)
                    self.deepAR.delegate = originalDelegate
                    completion(false, "Failed to save processed image: \(error.localizedDescription)")
                }
            } else {
                self.deepAR.changeLiveMode(wasLiveMode)
                self.deepAR.delegate = originalDelegate
                completion(false, "Failed to convert processed image to data")
            }
        }

        deepAR.delegate = tempDelegate
    }
}

/// Temporary delegate to handle screenshot callback
class TempScreenshotDelegate: NSObject, DeepARDelegate {
    private let screenshotCallback: (UIImage) -> Void

    init(screenshotCallback: @escaping (UIImage) -> Void) {
        self.screenshotCallback = screenshotCallback
        super.init()
    }

    func didTakeScreenshot(_ screenshot: UIImage!) {
        screenshotCallback(screenshot)
    }
}

/// Extension to convert UIImage to CVPixelBuffer
extension UIImage {
    func pixelBuffer() -> CVPixelBuffer? {
        let width = Int(self.size.width)
        let height = Int(self.size.height)

        let attributes = [
            kCVPixelBufferCGImageCompatibilityKey: kCFBooleanTrue,
            kCVPixelBufferCGBitmapContextCompatibilityKey: kCFBooleanTrue
        ] as CFDictionary

        var pixelBuffer: CVPixelBuffer?
        let status = CVPixelBufferCreate(
            kCFAllocatorDefault,
            width,
            height,
            kCVPixelFormatType_32ARGB,
            attributes,
            &pixelBuffer
        )

        guard status == kCVReturnSuccess, let buffer = pixelBuffer else {
            return nil
        }

        CVPixelBufferLockBaseAddress(buffer, CVPixelBufferLockFlags(rawValue: 0))
        let pixelData = CVPixelBufferGetBaseAddress(buffer)

        let rgbColorSpace = CGColorSpaceCreateDeviceRGB()
        let context = CGContext(
            data: pixelData,
            width: width,
            height: height,
            bitsPerComponent: 8,
            bytesPerRow: CVPixelBufferGetBytesPerRow(buffer),
            space: rgbColorSpace,
            bitmapInfo: CGImageAlphaInfo.noneSkipFirst.rawValue
        )

        context?.translateBy(x: 0, y: CGFloat(height))
        context?.scaleBy(x: 1, y: -1)

        UIGraphicsPushContext(context!)
        self.draw(in: CGRect(x: 0, y: 0, width: width, height: height))
        UIGraphicsPopContext()

        CVPixelBufferUnlockBaseAddress(buffer, CVPixelBufferLockFlags(rawValue: 0))

        return buffer
    }
}

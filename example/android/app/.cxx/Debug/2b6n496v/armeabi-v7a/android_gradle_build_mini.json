{"buildFiles": ["/Users/<USER>/Documents/DEV/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Documents/DEV/PUB-PACKAGES/deepar-flutter-plus/example/android/app/.cxx/Debug/2b6n496v/armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Documents/DEV/PUB-PACKAGES/deepar-flutter-plus/example/android/app/.cxx/Debug/2b6n496v/armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}
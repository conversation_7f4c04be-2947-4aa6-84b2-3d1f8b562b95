import 'dart:io';

import 'package:deepar_flutter_plus/deepar_flutter_plus.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';

class FaceDetectionExample extends StatefulWidget {
  const FaceDetectionExample({super.key});

  @override
  State<FaceDetectionExample> createState() => _FaceDetectionExampleState();
}

class _FaceDetectionExampleState extends State<FaceDetectionExample> {
  final DeepArControllerPlus _controller = DeepArControllerPlus();
  bool _isFaceDetected = false;
  String? _processedImagePath;
  String? _selectedImagePath;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _initializeDeepAR();
  }

  Future<void> _initializeDeepAR() async {
    // Request camera and microphone permissions
    await [Permission.camera, Permission.microphone].request();

    // Initialize DeepAR with your license keys
    final result = await _controller.initialize(
      androidLicenseKey:
          "d85643dd036c813eb547cd69ff945ca6165da09340382a0a1738ae8bf2ac10a1609047d22c35be36",
      iosLicenseKey: "your_ios_license_key",
      resolution: Resolution.medium,
    );

    if (result.success) {
      // Set up face detection callback
      _controller.onFaceVisibilityChanged = (isVisible) {
        setState(() {
          _isFaceDetected = isVisible;
        });
      };
    } else {
      debugPrint("DeepAR initialization failed: ${result.message}");
    }
  }

  Future<void> _pickImage() async {
    final ImagePicker picker = ImagePicker();
    final XFile? image = await picker.pickImage(source: ImageSource.gallery);

    if (image != null) {
      setState(() {
        _selectedImagePath = image.path;
        _processedImagePath = null;
      });
    }
  }

  Future<void> _processImage() async {
    if (_selectedImagePath == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please select an image first')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      debugPrint('Processing image: $_selectedImagePath');

      // Create output path
      final tempDir = await getTemporaryDirectory();
      final outputPath =
          '${tempDir.path}/processed_${DateTime.now().millisecondsSinceEpoch}.jpg';

      debugPrint('Output path: $outputPath');

      // Process the image with DeepAR
      final result = await _controller.processStaticImage(
        imagePath: _selectedImagePath!,
        outputPath: outputPath,
      );

      debugPrint('Processing result: $result');

      setState(() {
        _processedImagePath = result;
        _isLoading = false;
      });

      if (mounted && result != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Image processed successfully!')),
        );
      }
    } catch (e) {
      debugPrint('Error processing image: $e');
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error processing image: $e')),
        );
      }
    }
  }

  @override
  void dispose() {
    _controller.destroy();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Face Detection & Image Processing'),
      ),
      body: Column(
        children: [
          // DeepAR Preview
          Expanded(
            flex: 2,
            child: Stack(
              children: [
                DeepArPreviewPlus(
                  _controller,
                  onViewCreated: () {
                    // Load a face filter when view is created
                    // _controller.switchEffect('assets/effects/Test_2.deepar');
                    _controller.switchEffectWithSlot(
                      slot: 'face',
                      path: 'assets/effects/Test_2.deepar',
                    );
                    // _controller.switchEffect('assets/effects/Test_2.deepar');
                  },
                ),
                Positioned(
                  top: 20,
                  left: 20,
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: _isFaceDetected ? Colors.green : Colors.red,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      _isFaceDetected ? 'Face Detected' : 'No Face Detected',
                      style: const TextStyle(color: Colors.white),
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Static Image Processing
          Expanded(
            flex: 3,
            child: Container(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  const Text(
                    'Static Image Processing',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton(
                          onPressed: _pickImage,
                          child: const Text('Select Image'),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: ElevatedButton(
                          onPressed:
                              _selectedImagePath != null ? _processImage : null,
                          child: _isLoading
                              ? const SizedBox(
                                  width: 20,
                                  height: 20,
                                  child:
                                      CircularProgressIndicator(strokeWidth: 2),
                                )
                              : const Text('Apply Effect'),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: _processedImagePath != null
                              ? () {
                                  setState(() {
                                    _processedImagePath = null;
                                  });
                                }
                              : null,
                          child: const Text('Clear'),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Expanded(
                    child: Row(
                      children: [
                        // Original Image
                        Expanded(
                          child: Column(
                            children: [
                              const Text(
                                'Original',
                                style: TextStyle(fontWeight: FontWeight.bold),
                              ),
                              const SizedBox(height: 8),
                              Expanded(
                                child: _selectedImagePath != null
                                    ? Image.file(
                                        File(_selectedImagePath!),
                                        fit: BoxFit.contain,
                                      )
                                    : const Center(
                                        child: Text('No image selected'),
                                      ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(width: 8),
                        // Processed Image
                        Expanded(
                          child: Column(
                            children: [
                              const Text(
                                'Processed',
                                style: TextStyle(fontWeight: FontWeight.bold),
                              ),
                              const SizedBox(height: 8),
                              Expanded(
                                child: _processedImagePath != null
                                    ? Image.file(
                                        File(_processedImagePath!),
                                        fit: BoxFit.contain,
                                      )
                                    : const Center(
                                        child: Text('No processed image'),
                                      ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

package com.deepar.ai;

public  class MethodStrings {

    // Platform channels
    public final static String generalChannel = "deep_ar";
    public final static String cameraXChannel = "deep_ar/camerax";

    // Methods
    public final static  String initialize = "initialize";
    public final  static  String switchEffect = "switch_effect";
    public final static  String startCamera = "start_camera";
    public final static  String startRecordingVideo = "start_recording_video";
    public final static  String stopRecordingVideo = "stop_recording_video";
    public final  static  String dispose = "dispose";

    // Method arguments
    public final static  String licenseKey = "license_key";
    public final static  String effect = "effect";
    public final static  String resolution = "resolution";


}

group 'com.deepar.ai'
version '1.0'

buildscript {
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:7.1.3'
    }
}

rootProject.allprojects {
    repositories {
        google()
        mavenCentral()
        flatDir {
            dirs 'libs'
        }
    }
}

apply plugin: 'com.android.library'

android {
    namespace 'com.deepar.ai'
    compileSdkVersion 31

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    defaultConfig {
        minSdkVersion 21
    }
}

dependencies {
    api (name: 'deepar', ext: 'aar')
    implementation 'androidx.camera:camera-lifecycle:1.1.0-alpha01'
    implementation "androidx.camera:camera-camera2:1.1.0-alpha01"
}

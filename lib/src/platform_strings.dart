class PlatformStrings {
  // Platform channels
  static const String generalChannel = "deep_ar";
  static const String cameraXChannel = "deep_ar/camerax";
  static const String avCameraChannel = "deep_ar/view";

  // Methods
  static const String initialize = "initialize";
  static const String switchEffect = "switch_effect";
  static const String startCamera = "start_camera";
  static const String getResolution = "get_resolution";
  static const String startRecordingVideo = "start_recording_video";
  static const String stopRecordingVideo = "stop_recording_video";
  static const String checkVersion = "check_version";
  static const String dispose = "dispose";

  // Method arguments
  static const String licenseKey = "license_key";
  static const String effect = "effect";
  static const String resolution = "resolution";
}
